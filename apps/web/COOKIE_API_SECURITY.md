# Cookie.fun API Security & Rate Limiting

## 🔒 Security Measures Implemented

### 1. **Rate Limiting (Multi-Layer)**

#### User-Based Rate Limiting (tRPC Level)
- **Feature Type**: `COOKIE_API_CALLS`
- **Plan Limits**:
  - **Reply Guy**: 50 calls/month
  - **Reply God**: 200 calls/month  
  - **Team**: Unlimited calls
- **Protection**: Prevents users from exceeding their subscription limits
- **Error**: Returns `TOO_MANY_REQUESTS` with remaining count

#### Internal Rate Limiting (Client Level)
- **Limit**: 60 requests/minute per application instance
- **Protection**: Prevents API abuse and protects against runaway requests
- **Auto-Reset**: Counter resets every minute
- **Logging**: Tracks request count in logs

### 2. **Input Validation & Sanitization**

#### Endpoint Whitelisting
```typescript
// Only these endpoints are allowed:
/v3/sectors
/v3/account
/v3/project
/v3/account/smart-followers
/v3/project/search
/v3/project/mindshare-leaderboard
```

#### Request Body Sanitization
- **Allowed Fields**: Only whitelisted parameters accepted
- **XSS Protection**: Removes `<>'"&` characters
- **Length Limits**: Strings truncated to 100 characters
- **Type Validation**: Numbers must be 0-10000, booleans validated
- **Injection Prevention**: SQL injection patterns filtered

### 3. **API Key Protection**
- **Environment Variable**: `COOKIE_API_KEY` required
- **Header Format**: `X-API-Key: {key}` (not Bearer token)
- **Validation**: Checked before any request processing
- **Error Handling**: Clear error messages for missing keys

### 4. **Request Monitoring & Logging**
- **Request Tracking**: All API calls logged with metadata
- **Usage Recording**: Detailed usage tracking with:
  - Endpoint called
  - Timestamp
  - User agent
  - Request count
- **Error Logging**: Failed requests logged for monitoring

### 5. **Caching Layer**
- **TTL**: 10 minutes for API responses
- **Cache Key**: Includes method, URL, and body
- **Benefits**: Reduces API load and improves performance
- **Security**: Prevents cache poisoning with sanitized keys

## 🚀 API Configuration

### Base URL
```
Production: https://api.cookie.fun (404 - not working)
Staging: https://api.staging.cookie.fun ✅ WORKING
```

### Authentication
```typescript
headers: {
  'X-API-Key': process.env.COOKIE_API_KEY,
  'Content-Type': 'application/json'
}
```

### Response Format
```typescript
{
  ok: { /* actual data */ },
  success: true,
  error: null
}
```

## 📊 Usage Tracking

### Database Schema
```sql
-- New feature type added
ALTER TYPE feature_type ADD VALUE 'COOKIE_API_CALLS';

-- Plan features updated
INSERT INTO plan_features (plan_id, feature, limit) VALUES
  ('reply-guy', 'COOKIE_API_CALLS', 50),
  ('reply-god', 'COOKIE_API_CALLS', 200),
  ('team', 'COOKIE_API_CALLS', -1); -- unlimited
```

### Usage Recording
```typescript
await recordUsage(userId, FeatureType.COOKIE_API_CALLS, 1, {
  endpoint: 'getSectors',
  timestamp: new Date().toISOString(),
  userAgent: req.headers['user-agent']
});
```

## 🛡️ Security Testing

### Automated Tests
- ✅ Valid API calls work correctly
- ✅ Input sanitization prevents XSS/injection
- ✅ Endpoint validation blocks invalid URLs
- ✅ Rate limiting prevents abuse
- ✅ API key protection enforced

### Manual Testing
```bash
# Test rate limiting
bun run test-rate-limiting.js

# Test security measures
bun run test-security-measures.js

# Test API functionality
bun run test-final-api.js
```

## 🔧 Configuration

### Environment Variables
```bash
COOKIE_API_KEY=your_api_key_here
```

### Rate Limit Customization
```typescript
// In cookie-client.ts
private readonly MAX_REQUESTS_PER_MINUTE = 60; // Adjust as needed
```

### Plan Limits
```typescript
// In seed scripts - adjust per plan
COOKIE_API_CALLS: 50  // Reply Guy
COOKIE_API_CALLS: 200 // Reply God  
COOKIE_API_CALLS: -1  // Team (unlimited)
```

## 📈 Monitoring

### Key Metrics to Track
- API calls per user per month
- Rate limit violations
- Failed authentication attempts
- Response times and error rates
- Cache hit/miss ratios

### Logs to Monitor
```
🍪 Making Cookie.fun API request: GET /v3/sectors
🔒 Request count: 1/60 this minute
✅ Cookie.fun API success: /v3/sectors
🚨 Cookie.fun API internal rate limit exceeded
```

## 🚨 Security Alerts

### Watch For
- Unusual spike in API calls from single user
- Repeated authentication failures
- Attempts to access non-whitelisted endpoints
- Rate limit violations
- Malformed request bodies

### Response Actions
1. **Rate Limit Exceeded**: User gets clear error message
2. **Invalid Endpoint**: Request blocked, logged for review
3. **Missing API Key**: Request rejected immediately
4. **Malicious Input**: Sanitized automatically, logged
5. **Internal Rate Limit**: Request blocked, user notified

## ✅ Compliance

### Data Protection
- No sensitive data logged in request bodies
- API keys never exposed in logs
- User data sanitized before external API calls
- Minimal data retention for usage tracking

### Best Practices
- Principle of least privilege (whitelisted endpoints only)
- Defense in depth (multiple rate limiting layers)
- Input validation at every boundary
- Comprehensive logging for audit trails
- Graceful error handling with user-friendly messages

---

**Status**: ✅ All security measures implemented and tested
**Last Updated**: 2025-01-27
**Next Review**: Monitor usage patterns and adjust limits as needed
