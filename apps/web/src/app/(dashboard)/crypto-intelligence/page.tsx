"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { useUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import AuthenticatedNavbar from "@/components/authenticated-navbar"
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation"
import SectorOverviewCard from "@/components/crypto/SectorOverviewCard"
import SmartFollowersPanel from "@/components/crypto/SmartFollowersPanel"
import TrendingProjectsFeed from "@/components/crypto/TrendingProjectsFeed"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"

export default function CryptoIntelligencePage() {
  const { user, isLoaded } = useUser()
  const [selectedSector, setSelectedSector] = useState<string>("")
  const [timeframe, setTimeframe] = useState<"_7Days" | "_30Days">("_7Days")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date())

  // Get tRPC utils for cache management
  const utils = trpc.useUtils()

  // 🎯 OPTIMIZED SECTORS QUERY
  const { 
    data: sectors, 
    isLoading: sectorsLoading,
    error: sectorsError,
    refetch: refetchSectors,
    isFetching: sectorsFetching
  } = trpc.crypto.getSectors.useQuery(undefined, {
    // Cache Strategy
    staleTime: 15 * 60 * 1000, // Consider fresh for 15 minutes (sectors don't change often)
    cacheTime: 60 * 60 * 1000, // Keep in memory for 1 hour
    
    // Refetch Strategy
    refetchOnMount: true, // Always fetch fresh on component mount
    refetchOnWindowFocus: true, // Refresh when user returns to tab
    refetchOnReconnect: true, // Refresh on network reconnect
    
    // Error Handling
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  })

  // 🚀 OPTIMIZED TRENDING PROJECTS QUERY  
  const {
    data: trendingProjects,
    isLoading: trendingLoading,
    refetch: refetchTrending,
    error: trendingError,
    isFetching: trendingFetching
  } = trpc.crypto.getTrendingProjects.useQuery({
    sectorSlug: selectedSector || undefined,
    timeframe,
    limit: 20,
  }, {
    // Dependency Management
    enabled: !!sectors && !sectorsLoading, // Only fetch after sectors are loaded
    
    // Cache Strategy
    staleTime: 3 * 60 * 1000, // Consider fresh for 3 minutes (trending data changes frequently)
    cacheTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
    
    // Refetch Strategy
    refetchOnMount: true,
    refetchOnWindowFocus: false, // Prevent too frequent updates for trending data
    refetchOnReconnect: true,
    
    // Error Handling
    retry: 2,
    retryDelay: 1000,
  })

  // 🔄 MANUAL REFRESH FUNCTION
  const handleRefreshAll = useCallback(async () => {
    if (isRefreshing) return // Prevent multiple simultaneous refreshes
    
    setIsRefreshing(true)
    
    try {
      console.log('🔄 Refreshing all crypto intelligence data...')
      
      // Invalidate all relevant queries
      await utils.crypto.getSectors.invalidate()
      await utils.crypto.getTrendingProjects.invalidate()
      
      // Force refetch both queries in parallel
      const refreshPromises = [
        refetchSectors(),
        refetchTrending()
      ]
      
      await Promise.all(refreshPromises)
      
      setLastRefresh(new Date())
      toast.success("🎉 Crypto intelligence data refreshed!")
      
    } catch (error) {
      console.error('Failed to refresh crypto data:', error)
      toast.error("❌ Failed to refresh data. Please try again.")
    } finally {
      setIsRefreshing(false)
    }
  }, [isRefreshing, utils, refetchSectors, refetchTrending])

  // 🔧 AUTO-REFRESH ON DEPENDENCY CHANGE
  useEffect(() => {
    // Auto-refresh trending projects when sector or timeframe changes
    if (sectors && !sectorsLoading && !isRefreshing) {
      console.log(`🔄 Auto-refreshing trending projects for sector: ${selectedSector || 'all'}, timeframe: ${timeframe}`)
      refetchTrending()
    }
  }, [selectedSector, timeframe, sectors, sectorsLoading, refetchTrending, isRefreshing])

  // ⏰ BACKGROUND REFRESH (every 10 minutes)
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isRefreshing && !trendingFetching && !sectorsFetching) {
        console.log('⏰ Background refresh triggered')
        handleRefreshAll()
      }
    }, 10 * 60 * 1000) // 10 minutes

    return () => clearInterval(interval)
  }, [handleRefreshAll, isRefreshing, trendingFetching, sectorsFetching])

  // 📊 COMPUTED LOADING STATES
  const isInitialLoading = sectorsLoading && trendingLoading
  const hasAnyData = sectors || trendingProjects
  const hasError = sectorsError || trendingError
  const isDataStale = Date.now() - lastRefresh.getTime() > 10 * 60 * 1000 // 10 minutes

  if (!isLoaded) {
    return <div className="min-h-screen bg-app-background flex items-center justify-center">
      <ShardLoadingAnimation size={80} />
    </div>
  }

  if (!user) {
    redirect('/sign-in')
  }

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <div className="flex items-center justify-center gap-4 mb-4">
          <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline">
            CRYPTO INTELLIGENCE
          </h1>
          <Button
            onClick={handleRefreshAll}
            disabled={isRefreshing || isInitialLoading}
            variant="outline"
            size="sm"
            className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary min-h-[44px] min-w-[44px] transition-all"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing || sectorsFetching || trendingFetching ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <div className="space-y-2">
          <p className="text-sm sm:text-base text-app-headline/70 max-w-3xl mx-auto">
            Real-time crypto market intelligence, competitive analysis, and smart account discovery powered by Cookie.fun
          </p>
          {/* Data Freshness Indicator */}
          <div className="flex items-center justify-center gap-2 text-xs text-app-headline/50">
            <div className={`w-2 h-2 rounded-full ${isDataStale ? 'bg-yellow-500' : 'bg-green-500'} ${(sectorsFetching || trendingFetching) ? 'animate-pulse' : ''}`}></div>
            <span>
              Last updated: {lastRefresh.toLocaleTimeString()} 
              {isDataStale && ' (Data may be stale)'}
              {(sectorsFetching || trendingFetching) && ' (Updating...)'}
            </span>
          </div>
        </div>
      </header>

      <AuthenticatedNavbar currentPage="crypto-intelligence" />

      <main className="space-y-6 sm:space-y-8">
        {/* Main Intelligence Dashboard */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Sector Overview */}
          {sectors !== undefined && trendingProjects !== undefined ? (
            <SectorOverviewCard
              sectors={sectors}
              trendingProjects={trendingProjects}
              trendingLoading={trendingLoading}
              selectedSector={selectedSector}
              setSelectedSector={setSelectedSector}
              timeframe={timeframe}
              setTimeframe={setTimeframe}
              refetchTrending={refetchTrending}
            />
          ) : (
            <div className="bg-app-card border-app-stroke rounded-lg p-6 text-center">
              <div className="text-app-headline/60">Loading sector overview...</div>
            </div>
          )}

          {/* Right Column - Trending Projects */}
          {sectors !== undefined && trendingProjects !== undefined ? (
            <TrendingProjectsFeed
              sectors={sectors}
              trendingProjects={trendingProjects}
              trendingLoading={trendingLoading}
              trendingError={trendingError}
              selectedSector={selectedSector}
              setSelectedSector={setSelectedSector}
              timeframe={timeframe}
              setTimeframe={setTimeframe}
              refetchTrending={refetchTrending}
            />
          ) : (
            <div className="bg-app-card border-app-stroke rounded-lg p-6 text-center">
              <div className="text-app-headline/60">Loading trending projects...</div>
            </div>
          )}
        </div>

        {/* Smart Followers Discovery - Full Width */}
        <SmartFollowersPanel />
      </main>
    </div>
  )
}