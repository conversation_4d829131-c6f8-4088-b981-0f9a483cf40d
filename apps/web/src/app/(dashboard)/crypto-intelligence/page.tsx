"use client"

import type React from "react"
import { useUser } from '@clerk/nextjs'
import { redirect } from 'next/navigation'
import { useState } from "react"
import { RefreshCw } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import AuthenticatedNavbar from "@/components/authenticated-navbar"
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"
import SectorOverviewCard from "@/components/crypto/SectorOverviewCard"
import SmartFollowersPanel from "@/components/crypto/SmartFollowersPanel"
import CompetitiveAnalysisWidget from "@/components/crypto/CompetitiveAnalysisWidget"
import TrendingProjectsFeed from "@/components/crypto/TrendingProjectsFeed"

export default function CryptoIntelligencePage() {
  const { user, isLoaded } = useUser()
  const [refreshing, setRefreshing] = useState(false)

  // tRPC queries
  const utils = trpc.useUtils()

  if (!isLoaded) {
    return <div className="min-h-screen bg-app-background flex items-center justify-center">
      <ShardLoadingAnimation size={80} />
    </div>
  }
  
  if (!user) {
    redirect('/sign-in')
  }

  const handleRefreshData = async () => {
    setRefreshing(true)
    try {
      // Invalidate all crypto-related queries to refresh data
      await Promise.all([
        utils.crypto.getSectors.invalidate(),
        utils.crypto.getTrendingProjects.invalidate(),
      ])
      toast.success("Market data refreshed successfully!")
    } catch (error) {
      console.error('Failed to refresh data:', error)
      toast.error("Failed to refresh market data")
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <div className="min-h-screen p-3 sm:p-4 md:p-6 lg:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-6 sm:mb-8">
        <div className="flex items-center justify-center gap-4 mb-4">
          <h1 className="text-[clamp(1.75rem,5vw,3rem)] font-bold tracking-wider text-app-headline">
            CRYPTO INTELLIGENCE
          </h1>
          <Button
            onClick={handleRefreshData}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary min-h-[44px] min-w-[44px]"
          >
            <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </div>
        <p className="text-sm sm:text-base text-app-headline/70 max-w-3xl mx-auto">
          Real-time crypto market intelligence, competitive analysis, and smart account discovery powered by Cookie.fun
        </p>
      </header>

      <AuthenticatedNavbar currentPage="crypto-intelligence" />

      <main className="space-y-6 sm:space-y-8">
        {/* Main Intelligence Dashboard */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Primary Analysis */}
          <div className="xl:col-span-2 space-y-6">
            {/* Sector Overview */}
            <SectorOverviewCard />
            
            {/* Competitive Analysis */}
            <CompetitiveAnalysisWidget />
          </div>

          {/* Right Column - Discovery & Trends */}
          <div className="space-y-6">
            {/* Trending Projects */}
            <TrendingProjectsFeed />
          </div>
        </div>

        {/* Smart Followers Discovery - Full Width */}
        <SmartFollowersPanel />

        {/* Call to Action */}
        <Card className="bg-gradient-to-br from-app-main/5 to-app-main/10 border-app-main/20 text-app-headline shadow-md">
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold mb-2 text-app-headline">Ready to enhance your crypto social strategy?</h3>
            <p className="text-app-headline/70 mb-4 max-w-2xl mx-auto">
              Use these insights to discover high-influence accounts, track competitor performance, and enhance your AI responses with real-time market intelligence.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
                onClick={() => window.location.href = '/dashboard'}
              >
                Apply to Dashboard
              </Button>
              <Button 
                variant="outline" 
                className="border-app-stroke text-app-headline hover:bg-app-headline hover:text-app-background"
                onClick={() => window.location.href = '/reply-guy'}
              >
                Enhance AI Responses
              </Button>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}