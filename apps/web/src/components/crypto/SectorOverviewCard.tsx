"use client"

import { useState } from "react"
import { TrendingUp, TrendingDown, BarChart3, Loader2, ExternalLink } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select } from "@/components/ui/select"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"
import Mindshare<PERSON><PERSON> from "./charts/MindshareChart"

export default function SectorOverviewCard() {
  const [selectedSector, setSelectedSector] = useState<string>("")
  const [timeframe, setTimeframe] = useState<"_7Days" | "_30Days">("_7Days")

  // tRPC queries
  const { data: sectors, isLoading: sectorsLoading } = trpc.crypto.getSectors.useQuery()
  const { data: trendingProjects, isLoading: trendingLoading, refetch: refetchTrending } = trpc.crypto.getTrendingProjects.useQuery({
    sectorSlug: selectedSector || undefined,
    timeframe,
    limit: 5,
  })

  const handleSectorChange = (value: string) => {
    setSelectedSector(value)
  }

  const handleTimeframeChange = (value: "_7Days" | "_30Days") => {
    setTimeframe(value)
  }

  const getMarketSentiment = () => {
    if (!trendingProjects?.data || trendingProjects.data.length === 0) return "neutral"
    
    const avgMindshare = trendingProjects.data.reduce((sum, project) => sum + (project.mindshare || 0), 0) / trendingProjects.data.length
    
    if (avgMindshare > 70) return "bullish"
    if (avgMindshare < 30) return "bearish"
    return "neutral"
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case "bullish": return "text-green-600 bg-green-50 border-green-200"
      case "bearish": return "text-red-600 bg-red-50 border-red-200"
      default: return "text-yellow-600 bg-yellow-50 border-yellow-200"
    }
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case "bullish": return <TrendingUp className="w-4 h-4" />
      case "bearish": return <TrendingDown className="w-4 h-4" />
      default: return <BarChart3 className="w-4 h-4" />
    }
  }

  const marketSentiment = getMarketSentiment()

  return (
    <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="text-lg font-semibold text-app-headline">
            Sector Overview
          </CardTitle>
          <div className="flex flex-col sm:flex-row gap-2">
            {/* Timeframe Selector */}
            <div className="flex rounded-md border border-app-stroke overflow-hidden">
              <Button
                size="sm"
                variant={timeframe === "_7Days" ? "default" : "ghost"}
                onClick={() => handleTimeframeChange("_7Days")}
                className={`rounded-none text-xs ${
                  timeframe === "_7Days" 
                    ? "bg-app-main text-app-secondary" 
                    : "text-app-headline hover:bg-app-main/10"
                }`}
              >
                7D
              </Button>
              <Button
                size="sm"
                variant={timeframe === "_30Days" ? "default" : "ghost"}
                onClick={() => handleTimeframeChange("_30Days")}
                className={`rounded-none text-xs ${
                  timeframe === "_30Days" 
                    ? "bg-app-main text-app-secondary" 
                    : "text-app-headline hover:bg-app-main/10"
                }`}
              >
                30D
              </Button>
            </div>
            
            {/* Sector Selector */}
            <select 
              value={selectedSector}
              onChange={(e) => handleSectorChange(e.target.value)}
              className="px-3 py-1.5 text-xs border border-app-stroke rounded-md bg-app-background text-app-headline focus:border-app-main focus:outline-none min-w-[120px]"
              disabled={sectorsLoading}
            >
              <option value="">All Sectors</option>
              {sectors?.data?.map((sector) => (
                <option key={sector.id} value={sector.slug}>
                  {sector.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Market Sentiment Indicator */}
        <div className="flex items-center justify-between p-4 bg-app-background rounded-lg border border-app-stroke/50">
          <div>
            <p className="text-sm text-app-headline/60">Market Sentiment</p>
            <div className="flex items-center gap-2 mt-1">
              <Badge className={`${getSentimentColor(marketSentiment)} flex items-center gap-1`}>
                {getSentimentIcon(marketSentiment)}
                {marketSentiment.charAt(0).toUpperCase() + marketSentiment.slice(1)}
              </Badge>
              <span className="text-xs text-app-headline/50">
                {selectedSector ? `${selectedSector} sector` : "Overall market"}
              </span>
            </div>
          </div>
          {trendingLoading && (
            <Loader2 className="w-5 h-5 animate-spin text-app-main" />
          )}
        </div>

        {/* Trending Projects */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-app-headline">
              Top Trending Projects
            </h4>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => refetchTrending()}
              disabled={trendingLoading}
              className="text-xs text-app-headline/60 hover:text-app-main p-1 min-h-[32px] min-w-[32px]"
            >
              <TrendingUp className="w-4 h-4" />
            </Button>
          </div>

          {trendingLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-app-main" />
            </div>
          ) : trendingProjects?.data && trendingProjects.data.length > 0 ? (
            <div className="space-y-3">
              {trendingProjects.data.map((project, index) => (
                <div 
                  key={project.slug} 
                  className="flex items-center justify-between p-3 bg-app-background rounded-md border border-app-stroke/30 hover:border-app-stroke/60 transition-colors"
                >
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0 w-6 h-6 bg-app-main/10 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-app-main">
                        {index + 1}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h5 className="text-sm font-medium text-app-headline truncate">
                          {project.name}
                        </h5>
                        {project.symbol && (
                          <Badge variant="outline" className="text-xs px-1 py-0 text-app-headline/60 border-app-stroke">
                            {project.symbol}
                          </Badge>
                        )}
                      </div>
                      {project.sector && (
                        <p className="text-xs text-app-headline/50 mt-0.5">
                          {project.sector}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 flex-shrink-0">
                    {project.mindshare && (
                      <div className="text-right">
                        <p className="text-sm font-medium text-app-main">
                          {project.mindshare}
                        </p>
                        <p className="text-xs text-app-headline/50">mindshare</p>
                      </div>
                    )}
                    
                    {project.twitterUrl && (
                      <Button
                        size="sm"
                        variant="ghost"
                        className="p-1 min-h-[32px] min-w-[32px] text-app-headline/50 hover:text-app-main"
                        onClick={() => window.open(project.twitterUrl, '_blank')}
                      >
                        <ExternalLink className="w-3 h-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <BarChart3 className="w-12 h-12 text-app-main/30 mx-auto mb-3" />
              <p className="text-sm text-app-headline/60 mb-2">
                No trending projects found
              </p>
              <p className="text-xs text-app-headline/40">
                {selectedSector ? `Try selecting a different sector` : `Select a specific sector to see trends`}
              </p>
            </div>
          )}
        </div>

        {/* Optional: Mini Chart Preview */}
        {trendingProjects?.data && trendingProjects.data.length > 0 && (
          <div className="pt-4 border-t border-app-stroke/30">
            <MindshareChart 
              projects={trendingProjects.data.slice(0, 3)} 
              timeframe={timeframe}
              compact={true}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}