"use client"

import { useState, useEffect } from "react"
import { Tren<PERSON>Up, <PERSON>Link, Loader2, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON>pR<PERSON>, ArrowDownRight } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

interface TrendingProjectsFeedProps {
  sectors?: any
  trendingProjects?: any
  trendingLoading: boolean
  trendingError?: any
  selectedSector: string
  setSelectedSector: (sector: string) => void
  timeframe: "_7Days" | "_30Days"
  setTimeframe: (timeframe: "_7Days" | "_30Days") => void
  refetchTrending: () => void
}

interface TrendingProject {
  name: string
  slug: string
  symbol?: string
  sector?: string
  mindshare?: number
  smartEngagementPoints?: number
  trending?: boolean
  twitterUrl?: string
  websiteUrl?: string
  description?: string
}

export default function TrendingProjectsFeed({
  sectors,
  trendingProjects,
  trendingLoading,
  trendingError,
  selectedSector,
  setSelectedSector,
  timeframe,
  setTimeframe,
  refetchTrending
}: TrendingProjectsFeedProps) {
  const [autoRefresh, setAutoRefresh] = useState(false)

  // Safety check for props
  if (!sectors && !trendingProjects) {
    return (
      <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
        <CardContent className="p-6 text-center">
          <div className="text-app-headline/60">Loading trending projects...</div>
        </CardContent>
      </Card>
    )
  }



  // Auto-refresh every 5 minutes if enabled
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      refetchTrending()
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearInterval(interval)
  }, [autoRefresh, refetchTrending])

  const handleRefresh = () => {
    try {
      refetchTrending()
      toast.success("Trending data refreshed!")
    } catch (error) {
      toast.error("Failed to refresh trending data")
    }
  }

  const getMindshareColor = (mindshare?: number) => {
    if (!mindshare) return "text-gray-500"
    if (mindshare > 80) return "text-green-600"
    if (mindshare > 60) return "text-blue-600"
    if (mindshare > 40) return "text-yellow-600"
    return "text-gray-500"
  }

  const getTrendDirection = (mindshare?: number) => {
    if (!mindshare) return null
    
    // Simulate trend direction based on mindshare value
    // In a real implementation, this would compare current vs previous values
    const isUpTrend = mindshare > 50
    const magnitude = Math.abs(mindshare - 50)
    
    return {
      isUp: isUpTrend,
      percentage: Math.min(magnitude, 25).toFixed(0)
    }
  }

  const getSectorBadgeColor = (sector?: string) => {
    const colors: Record<string, string> = {
      defi: "bg-blue-50 text-blue-700 border-blue-200",
      gaming: "bg-purple-50 text-purple-700 border-purple-200",
      ai: "bg-green-50 text-green-700 border-green-200",
      infrastructure: "bg-orange-50 text-orange-700 border-orange-200",
      meme: "bg-pink-50 text-pink-700 border-pink-200",
    }
    
    return colors[sector?.toLowerCase() || ""] || "bg-gray-50 text-gray-700 border-gray-200"
  }

  return (
    <Card className="bg-app-card border-app-stroke text-app-headline shadow-md">
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <CardTitle className="text-lg font-semibold text-app-headline flex items-center gap-2">
            <TrendingUp className="w-5 h-5 text-app-main" />
            Trending Projects
          </CardTitle>
          
          <div className="flex items-center gap-2">
            {/* Auto-refresh toggle */}
            <Button
              size="sm"
              variant={autoRefresh ? "default" : "ghost"}
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={`text-xs px-2 py-1 ${
                autoRefresh 
                  ? "bg-app-main text-app-secondary" 
                  : "text-app-headline/60 hover:text-app-main"
              }`}
            >
              {autoRefresh ? "Live" : "Manual"}
            </Button>
            
            {/* Manual refresh */}
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRefresh}
              disabled={trendingLoading}
              className="text-app-headline/60 hover:text-app-main p-1 min-h-[32px] min-w-[32px]"
            >
              <RefreshCw className={`w-4 h-4 ${trendingLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-2 mt-4">
          {/* Timeframe Selector */}
          <div className="flex rounded-md border border-app-stroke overflow-hidden">
            <Button
              size="sm"
              variant={timeframe === "_7Days" ? "default" : "ghost"}
              onClick={() => setTimeframe("_7Days")}
              className={`rounded-none text-xs ${
                timeframe === "_7Days" 
                  ? "bg-app-main text-app-secondary" 
                  : "text-app-headline hover:bg-app-main/10"
              }`}
            >
              7 Days
            </Button>
            <Button
              size="sm"
              variant={timeframe === "_30Days" ? "default" : "ghost"}
              onClick={() => setTimeframe("_30Days")}
              className={`rounded-none text-xs ${
                timeframe === "_30Days" 
                  ? "bg-app-main text-app-secondary" 
                  : "text-app-headline hover:bg-app-main/10"
              }`}
            >
              30 Days
            </Button>
          </div>
          
          {/* Sector Filter */}
          <select 
            value={selectedSector}
            onChange={(e) => setSelectedSector(e.target.value)}
            className="px-3 py-1.5 text-xs border border-app-stroke rounded-md bg-app-background text-app-headline focus:border-app-main focus:outline-none min-w-[120px]"
          >
            <option value="">All Sectors</option>
            {Array.isArray(sectors?.data) ? sectors.data.map((sector: any) => (
              <option key={sector.id || sector.slug} value={sector.slug}>
                {sector.name}
              </option>
            )) : Array.isArray(sectors) ? sectors.map((sector: any) => (
              <option key={sector.id || sector.slug} value={sector.slug}>
                {sector.name}
              </option>
            )) : null}
          </select>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {trendingLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin text-app-main" />
          </div>
        ) : Array.isArray(trendingProjects?.data) && trendingProjects.data.length > 0 ? (
          <div className="space-y-3">
            {trendingProjects.data.slice(0, 10).map((project: any, index: number) => {
              const trend = getTrendDirection(project.mindshare)
              
              return (
                <div 
                  key={project.slug}
                  className="group p-4 bg-app-background rounded-lg border border-app-stroke/30 hover:border-app-stroke/60 transition-all duration-200 hover:shadow-sm"
                >
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex items-start gap-3 flex-1 min-w-0">
                      {/* Rank Badge */}
                      <div className="flex-shrink-0 w-8 h-8 bg-app-main/10 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-app-main">
                          {index + 1}
                        </span>
                      </div>

                      {/* Project Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="text-sm font-semibold text-app-headline truncate">
                            {project.name}
                          </h5>
                          
                          {project.symbol && (
                            <Badge variant="outline" className="text-xs px-2 py-0.5 text-app-headline/60 border-app-stroke">
                              {project.symbol}
                            </Badge>
                          )}
                          
                          {trend && (
                            <div className={`flex items-center gap-1 text-xs ${
                              trend.isUp ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {trend.isUp ? (
                                <ArrowUpRight className="w-3 h-3" />
                              ) : (
                                <ArrowDownRight className="w-3 h-3" />
                              )}
                              {trend.percentage}%
                            </div>
                          )}
                        </div>

                        {/* Sector & Description */}
                        <div className="space-y-2">
                          {project.sector && (
                            <Badge className={`text-xs px-2 py-0.5 ${getSectorBadgeColor(project.sector)}`}>
                              {project.sector}
                            </Badge>
                          )}
                          
                          {project.description && (
                            <p className="text-xs text-app-headline/60 line-clamp-2 leading-relaxed">
                              {project.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Metrics & Actions */}
                    <div className="flex flex-col items-end gap-2 flex-shrink-0">
                      {/* Mindshare Score */}
                      {project.mindshare && (
                        <div className="text-right">
                          <p className={`text-lg font-bold ${getMindshareColor(project.mindshare)}`}>
                            {project.mindshare}
                          </p>
                          <p className="text-xs text-app-headline/50">mindshare</p>
                        </div>
                      )}

                      {/* Engagement Points */}
                      {project.smartEngagementPoints && (
                        <div className="text-right">
                          <p className="text-sm font-medium text-app-headline">
                            {project.smartEngagementPoints}
                          </p>
                          <p className="text-xs text-app-headline/50">engagement</p>
                        </div>
                      )}

                      {/* Action Links */}
                      <div className="flex items-center gap-1">
                        {project.twitterUrl && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => window.open(project.twitterUrl, '_blank')}
                            className="text-app-headline/50 hover:text-app-main p-1 min-h-[28px] min-w-[28px] opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                        )}
                        
                        {project.websiteUrl && (
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => window.open(project.websiteUrl, '_blank')}
                            className="text-app-headline/50 hover:text-app-main p-1 min-h-[28px] min-w-[28px] opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <TrendingUp className="w-3 h-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        ) : trendingError ? (
          <div className="text-center py-8">
            <TrendingUp className="w-12 h-12 text-red-400/30 mx-auto mb-3" />
            <p className="text-sm text-app-headline/60 mb-2">
              Failed to load trending projects
            </p>
            <p className="text-xs text-app-headline/40 mb-4">
              {trendingError.message}
            </p>
            <Button
              size="sm"
              variant="outline"
              onClick={() => refetchTrending()}
              className="text-app-headline border-app-stroke hover:bg-app-main hover:text-app-secondary"
            >
              Try Again
            </Button>
          </div>
        ) : (
          <div className="text-center py-8">
            <TrendingUp className="w-12 h-12 text-app-main/30 mx-auto mb-3" />
            <p className="text-sm text-app-headline/60 mb-2">
              No trending projects found
            </p>
            <p className="text-xs text-app-headline/40 mb-4">
              {selectedSector ? `No trends in ${selectedSector} sector` : "Loading trending projects..."}
            </p>
            <Button
              size="sm"
              variant="outline"
              onClick={() => refetchTrending()}
              className="text-app-headline border-app-stroke hover:bg-app-main hover:text-app-secondary"
            >
              Refresh Data
            </Button>
          </div>
        )}

        {/* Status Footer */}
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-app-stroke/30">
          <div className="flex items-center gap-2 text-xs text-app-headline/50">
            <div className={`w-2 h-2 rounded-full ${autoRefresh ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
            <span>
              {autoRefresh ? 'Auto-refreshing' : 'Manual refresh'} • {timeframe === "_7Days" ? "7 days" : "30 days"}
            </span>
          </div>
          
          {trendingProjects?.data && (
            <span className="text-xs text-app-headline/50">
              {trendingProjects.data.length} projects
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}