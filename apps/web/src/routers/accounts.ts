/**
 * Accounts Router for BuddyChip tRPC API - Fixed version matching schema
 * 
 * <PERSON>les monitored account management including:
 * - Adding/removing Twitter accounts to monitor
 * - Managing account status and settings
 * - Account statistics and monitoring
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { protectedProcedure, createTRPCRouter } from '../lib/trpc';
import { twitterClient } from '../lib/twitter-client';
import { canUserUseFeature, logUsage } from '../lib/user-service';
import { MentionSyncService, type SyncResult } from '../lib/mention-sync-service';
import { sanitizeTwitterHandle, logSecurityEvent, validateCUID } from '../lib/security-utils';
import { cookieClient } from '../lib/cookie-client';
import { checkRateLimit, recordUsage } from '../lib/db-utils';
import { FeatureType } from '../../prisma/generated/index.js';

/**
 * Helper function to verify account ownership
 */
async function verifyAccountOwnership(prisma: any, accountId: string, userId: string) {
  console.log('🔍 AccountsRouter: Verifying ownership of account:', accountId, 'for user:', userId);

  if (!validateCUID(accountId)) {
    logSecurityEvent({
      type: 'INVALID_INPUT',
      userId,
      details: `Invalid account ID format: ${accountId}`
    });
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: 'Invalid account ID format',
    });
  }

  const account = await prisma.monitoredAccount.findFirst({
    where: {
      id: accountId,
      userId: userId,
    },
  });

  if (!account) {
    logSecurityEvent({
      type: 'UNAUTHORIZED_ACCESS',
      userId,
      details: `Attempted to access account ${accountId} without ownership`
    });
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: 'Account not found or access denied',
    });
  }

  return account;
}

export const accountsRouter = createTRPCRouter({
  /**
   * Get all monitored accounts for the current user
   */
  getMonitored: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const accounts = await ctx.prisma.monitoredAccount.findMany({
          where: {
            userId: ctx.userId!,
          },
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            _count: {
              select: {
                mentions: true,
              },
            },
          },
        });

        return {
          success: true,
          accounts: accounts.map(account => ({
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            avatar: account.avatarUrl,
            isActive: account.isActive,
            platform: 'twitter',
            followerCount: 0, // Not available in current schema
            createdAt: account.createdAt,
            lastSyncAt: account.lastCheckedAt,
            mentionsCount: account._count.mentions,
          })),
        };
      } catch (error: any) {
        console.error('Get monitored accounts error:', error);
        
        // Handle specific database connection errors
        if (error.message?.includes('Can\'t reach database server') || 
            error.message?.includes('Database is temporarily unavailable') ||
            error.name === 'PrismaClientInitializationError') {
          throw new TRPCError({
            code: 'SERVICE_UNAVAILABLE',
            message: 'Database is temporarily unavailable. Please try again in a few moments.',
          });
        }
        
        // Handle other known Prisma errors
        if (error.name === 'PrismaClientKnownRequestError') {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Database query failed. Please check your request.',
          });
        }
        
        // Generic fallback
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch monitored accounts',
        });
      }
    }),

  /**
   * Add a new Twitter account to monitor
   */
  add: protectedProcedure
    .input(z.object({
      handle: z.string().min(1, 'Twitter handle is required'),
      syncSettings: z.object({
        syncMentions: z.boolean().default(true),
        syncUserTweets: z.boolean().default(false),
        syncReplies: z.boolean().default(false),
        syncRetweets: z.boolean().default(true),
      }).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      console.log('🚀 Starting account addition process for:', input.handle);
      console.log('👤 User ID:', ctx.userId);

      try {
        // Check if user can add more monitored accounts
        console.log('🔍 Checking feature limits for MONITORED_ACCOUNTS...');
        const featureCheck = await canUserUseFeature(ctx.userId!, 'MONITORED_ACCOUNTS');
        console.log('📊 Feature check result:', featureCheck);

        if (!featureCheck.allowed) {
          console.log('❌ Feature limit exceeded:', featureCheck);
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: `You've reached your limit of ${featureCheck.limit} monitored accounts. Upgrade your plan to monitor more accounts.`,
          });
        }

        // Clean and validate the handle
        console.log('🧹 Cleaning handle:', input.handle);
        const cleanHandle = input.handle.replace('@', '').toLowerCase();
        console.log('✨ Clean handle:', cleanHandle);

        if (!twitterClient.validateTwitterHandle(cleanHandle)) {
          console.log('❌ Invalid handle format:', cleanHandle);
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Invalid Twitter handle format. Handles must be 1-15 characters, alphanumeric and underscores only.',
          });
        }
        console.log('✅ Handle validation passed');

        // Check if account is already being monitored
        console.log('🔍 Checking for existing account...');
        const existingAccount = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            userId: ctx.userId!,
            twitterHandle: cleanHandle,
          },
        });
        console.log('📋 Existing account check result:', existingAccount ? 'Found' : 'Not found');

        if (existingAccount) {
          console.log('❌ Account already monitored:', cleanHandle);
          throw new TRPCError({
            code: 'CONFLICT',
            message: `You're already monitoring @${cleanHandle}`,
          });
        }

        // Fetch user info from Twitter to validate account exists
        console.log('🐦 Fetching Twitter user info for:', cleanHandle);
        let userInfo;
        try {
          const response = await twitterClient.getUserInfo(cleanHandle);
          userInfo = response;
          console.log('✅ Twitter user info fetched successfully:', {
            id: userInfo.id,
            userName: userInfo.userName,
            name: userInfo.name
          });
        } catch (error) {
          console.error('❌ Twitter API error:', error);
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: `Twitter account @${cleanHandle} not found or is private`,
          });
        }

        // Create the monitored account
        console.log('💾 Creating monitored account in database...');
        
        // Get sync settings from input or use defaults
        const syncSettings = input.syncSettings || {
          syncMentions: true,
          syncUserTweets: false,
          syncReplies: false,
          syncRetweets: true,
        };
        
        const accountData = {
          userId: ctx.userId!,
          twitterHandle: cleanHandle,
          twitterId: userInfo.id,
          displayName: userInfo.name || cleanHandle,
          avatarUrl: userInfo.profilePicture,
          isActive: true,
          lastCheckedAt: new Date(),
          syncMentions: syncSettings.syncMentions,
          syncUserTweets: syncSettings.syncUserTweets,
          syncReplies: syncSettings.syncReplies,
          syncRetweets: syncSettings.syncRetweets,
        };
        console.log('📝 Account data to create:', accountData);

        const account = await ctx.prisma.monitoredAccount.create({
          data: accountData,
        });
        console.log('✅ Account created successfully:', account.id);

        // Log usage
        console.log('📊 Logging usage for MONITORED_ACCOUNTS...');
        await logUsage(ctx.userId!, 'MONITORED_ACCOUNTS', 1);
        console.log('✅ Usage logged successfully');

        // Automatically sync mentions for the newly added account
        console.log('🔄 Starting auto-sync for newly added account...');
        let syncResult: SyncResult | null = null;
        let mentionsCount = 0;
        
        try {
          console.log('🔧 Creating MentionSyncService instance...');
          const syncService = new MentionSyncService(ctx.prisma);
          console.log('✅ MentionSyncService created successfully');
          
          console.log(`📡 Calling syncAccountMentions for account ${account.id}, user ${ctx.userId}`);
          syncResult = await syncService.syncAccountMentions(account.id, ctx.userId!);
          console.log('📊 Sync result:', syncResult);
          
          if (syncResult?.success) {
            mentionsCount = syncResult.newMentions || 0;
            console.log(`✅ Initial sync completed: ${mentionsCount} mentions found for @${cleanHandle}`);
          } else {
            console.log(`⚠️ Initial sync had issues: ${syncResult?.error || 'Unknown sync error'}`);
          }
        } catch (syncError) {
          console.error('❌ Auto-sync failed (non-critical):', syncError);
          console.error('🔍 Sync error type:', typeof syncError);
          console.error('🔍 Sync error message:', (syncError as any)?.message);
          console.error('🔍 Sync error stack:', (syncError as any)?.stack);
          // Don't fail the entire account creation if sync fails
        }

        const result = {
          success: true,
          account: {
            id: account.id,
            handle: account.twitterHandle,
            displayName: account.displayName,
            avatar: account.avatarUrl,
            isActive: account.isActive,
            platform: 'twitter',
            followerCount: 0,
            createdAt: account.createdAt,
            lastSyncAt: account.lastCheckedAt,
            mentionsCount: mentionsCount, // Show actual synced mentions count
          },
          message: (syncResult as SyncResult | null)?.success 
            ? `Now monitoring @${cleanHandle}${mentionsCount > 0 ? ` (${mentionsCount} mentions found)` : ''}`
            : `Now monitoring @${cleanHandle}`,
          autoSyncResult: syncResult, // Include sync result for debugging/UI feedback
        };
        console.log('🎉 Account addition completed successfully:', result);
        return result;
      } catch (error) {
        console.error('❌ Add monitored account error:', error);

        if (error instanceof TRPCError) {
          console.error('🔴 TRPCError details:', {
            code: error.code,
            message: error.message,
            cause: error.cause
          });
          throw error;
        }

        console.error('🔴 Unexpected error:', error);
        console.error('🔍 Error type:', typeof error);
        console.error('🔍 Error constructor:', (error as any)?.constructor?.name);
        console.error('🔍 Error message:', (error as any)?.message);
        console.error('🔍 Error stack:', (error as any)?.stack);
        
        // Provide more specific error message
        let errorMessage = 'Failed to add monitored account';
        if ((error as any)?.message) {
          errorMessage = `Failed to add monitored account: ${(error as any).message}`;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: errorMessage,
        });
      }
    }),

  /**
   * Remove a monitored account
   */
  remove: protectedProcedure
    .input(z.object({
      accountId: z.string().cuid('Invalid account ID'),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the account belongs to the user
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Monitored account not found',
          });
        }

        // Delete the account and all related mentions
        await ctx.prisma.monitoredAccount.delete({
          where: {
            id: input.accountId,
          },
        });

        return {
          success: true,
          message: `Stopped monitoring @${account.twitterHandle}`,
        };
      } catch (error) {
        console.error('Remove monitored account error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to remove monitored account',
        });
      }
    }),

  /**
   * Toggle monitoring status for an account
   */
  toggleStatus: protectedProcedure
    .input(z.object({
      accountId: z.string().cuid('Invalid account ID'),
      isActive: z.boolean(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the account belongs to the user
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Monitored account not found',
          });
        }

        // Update the status
        const updatedAccount = await ctx.prisma.monitoredAccount.update({
          where: {
            id: input.accountId,
          },
          data: {
            isActive: input.isActive,
          },
        });

        return {
          success: true,
          account: updatedAccount,
          message: `@${account.twitterHandle} monitoring ${input.isActive ? 'enabled' : 'disabled'}`,
        };
      } catch (error) {
        console.error('Toggle account status error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update account status',
        });
      }
    }),

  /**
   * Update sync settings for a monitored account
   */
  updateSyncSettings: protectedProcedure
    .input(z.object({
      accountId: z.string().cuid('Invalid account ID'),
      syncSettings: z.object({
        syncMentions: z.boolean(),
        syncUserTweets: z.boolean(),
        syncReplies: z.boolean(),
        syncRetweets: z.boolean(),
      }),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Verify the account belongs to the user
        const account = await ctx.prisma.monitoredAccount.findFirst({
          where: {
            id: input.accountId,
            userId: ctx.userId!,
          },
        });

        if (!account) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Account not found or unauthorized',
          });
        }

        console.log(`⚙️ Updating sync settings for @${account.twitterHandle}:`, input.syncSettings);

        // Update the account sync settings
        const updatedAccount = await ctx.prisma.monitoredAccount.update({
          where: { id: input.accountId },
          data: {
            syncMentions: input.syncSettings.syncMentions,
            syncUserTweets: input.syncSettings.syncUserTweets,
            syncReplies: input.syncSettings.syncReplies,
            syncRetweets: input.syncSettings.syncRetweets,
          },
        });

        // Trigger immediate sync if settings were changed and account is active
        if (account.isActive) {
          console.log(`🔄 Triggering immediate sync for @${account.twitterHandle} with new settings`);
          try {
            const syncService = new MentionSyncService(ctx.prisma);
            await syncService.syncAccountMentions(input.accountId, ctx.userId!);
          } catch (syncError) {
            console.error('Sync after settings update failed:', syncError);
            // Don't throw here as the settings were successfully updated
          }
        }

        return {
          success: true,
          account: updatedAccount,
          message: `Sync settings updated for @${account.twitterHandle}`,
        };
      } catch (error) {
        console.error('Update sync settings error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update sync settings',
        });
      }
    }),

  /**
   * Discover smart accounts to monitor based on crypto sector and influence
   */
  discoverSmartAccounts: protectedProcedure
    .input(z.object({
      targetSector: z.string().optional(),
      seedAccount: z.string().optional(), // Twitter handle to use as seed for smart followers
      limit: z.number().min(1).max(50).default(10),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🔍 AccountsRouter: Discovering smart accounts for user:', ctx.userId);
        console.log('📝 AccountsRouter: Discovery params:', input);

        let discoveredAccounts = [];

        // Strategy 1: If we have a seed account, use smart followers
        if (input.seedAccount) {
          const cleanHandle = sanitizeTwitterHandle(input.seedAccount);
          
          console.log('🌱 Using seed account for discovery:', cleanHandle);
          
          // Get smart followers from the seed account
          const smartFollowersResponse = await cookieClient.getSmartFollowers({
            username: cleanHandle,
            limit: input.limit * 2, // Get more to filter out existing ones
          });

          discoveredAccounts = smartFollowersResponse.data.map(follower => ({
            handle: follower.username,
            displayName: follower.displayName,
            avatarUrl: follower.profileImageUrl,
            followerCount: follower.followerCount,
            smartScore: follower.smartScore,
            influence: follower.influence,
            sector: follower.sector || input.targetSector,
            source: 'smart_followers',
            seedAccount: cleanHandle,
          }));
        }

        // Strategy 2: If we have a target sector, get trending accounts in that sector
        if (input.targetSector && discoveredAccounts.length < input.limit) {
          console.log('🏢 Discovering trending accounts in sector:', input.targetSector);
          
          try {
            const trendingProjects = await cookieClient.getTrendingProjects(input.targetSector, '_7Days');
            
            // Extract accounts from trending projects (if they have Twitter handles)
            const sectorAccounts = trendingProjects
              .filter(project => project.twitterUrl)
              .map(project => {
                // Extract handle from Twitter URL
                const handle = project.twitterUrl ? twitterClient.extractUsername(project.twitterUrl) : null;
                if (!handle) return null;
                
                return {
                  handle,
                  displayName: project.name,
                  avatarUrl: undefined,
                  followerCount: undefined,
                  smartScore: project.mindshare,
                  influence: project.smartEngagementPoints,
                  sector: project.sector || input.targetSector,
                  source: 'trending_projects',
                  projectName: project.name,
                };
              })
              .filter(Boolean)
              .slice(0, input.limit - discoveredAccounts.length);

            discoveredAccounts.push(...sectorAccounts);
          } catch (error) {
            console.warn('⚠️ Failed to get trending projects for sector discovery:', error);
          }
        }

        // Get existing monitored accounts to filter out duplicates
        const existingAccounts = await ctx.prisma.monitoredAccount.findMany({
          where: { userId: ctx.userId },
          select: { twitterHandle: true },
        });
        
        const existingHandles = new Set(existingAccounts.map(acc => acc.twitterHandle.toLowerCase()));

        // Filter out accounts already being monitored
        const newAccounts = discoveredAccounts
          .filter(account => !existingHandles.has(account.handle.toLowerCase()))
          .slice(0, input.limit);

        // Enrich with Twitter data for accounts we don't have complete info
        const enrichedAccounts = await Promise.allSettled(
          newAccounts.map(async (account) => {
            try {
              // If we're missing basic info, fetch from Twitter
              if (!account.followerCount || !account.displayName) {
                const twitterInfo = await twitterClient.getUserInfo(account.handle);
                return {
                  ...account,
                  displayName: account.displayName || twitterInfo.name,
                  avatarUrl: account.avatarUrl || twitterInfo.profilePicture,
                  followerCount: account.followerCount || twitterInfo.followers,
                  verified: twitterInfo.isVerified,
                };
              }
              return account;
            } catch (error) {
              console.warn(`⚠️ Failed to enrich account @${account.handle}:`, error);
              return account; // Return as-is if enrichment fails
            }
          })
        );

        // Extract successful results
        const successfulAccounts = enrichedAccounts
          .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
          .map(result => result.value)
          .sort((a, b) => {
            // Sort by influence/smart score, then by follower count
            const scoreA = a.smartScore || a.influence || 0;
            const scoreB = b.smartScore || b.influence || 0;
            if (scoreA !== scoreB) return scoreB - scoreA;
            return (b.followerCount || 0) - (a.followerCount || 0);
          });

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1, {
          feature: 'smart_account_discovery',
          sector: input.targetSector,
          seedAccount: input.seedAccount,
          resultCount: successfulAccounts.length,
        });

        return {
          success: true,
          accounts: successfulAccounts,
          totalFound: successfulAccounts.length,
          strategies: {
            smartFollowers: input.seedAccount ? true : false,
            sectorTrending: input.targetSector ? true : false,
          },
          suggestions: {
            message: successfulAccounts.length > 0 
              ? `Found ${successfulAccounts.length} high-influence accounts you should consider monitoring`
              : 'No new accounts found. Try different search criteria or check your existing monitored accounts.',
            nextSteps: successfulAccounts.length > 0 
              ? 'Review the suggested accounts and add the most relevant ones to your monitoring list.'
              : input.seedAccount 
                ? 'Try using a different seed account or explore trending projects in your sector of interest.'
                : 'Provide a seed account (someone you already follow) to discover their influential followers.',
          },
        };

      } catch (error) {
        console.error('❌ Smart account discovery error:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to discover smart accounts. Please try again.',
        });
      }
    }),

  /**
   * Get sector-based account recommendations
   */
  getSectorRecommendations: protectedProcedure
    .input(z.object({
      sector: z.string().min(1, 'Sector is required'),
      limit: z.number().min(1).max(20).default(5),
    }))
    .query(async ({ ctx, input }) => {
      // Check rate limits (lighter operation)
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🏢 AccountsRouter: Getting sector recommendations for:', input.sector);

        // Get trending projects in the specified sector
        const trendingProjects = await cookieClient.getTrendingProjects(input.sector, '_7Days');
        
        // Extract account recommendations from trending projects
        const recommendations = trendingProjects
          .slice(0, input.limit)
          .map(project => ({
            projectName: project.name,
            projectSlug: project.slug,
            symbol: project.symbol,
            description: project.description,
            sector: project.sector,
            mindshare: project.mindshare,
            twitterHandle: project.twitterUrl ? twitterClient.extractUsername(project.twitterUrl) : null,
            websiteUrl: project.websiteUrl,
            reasoning: `Trending in ${project.sector} with ${project.mindshare || 0} mindshare points`,
          }))
          .filter(rec => rec.twitterHandle); // Only include projects with Twitter accounts

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1, {
          feature: 'sector_recommendations',
          sector: input.sector,
          resultCount: recommendations.length,
        });

        return {
          success: true,
          sector: input.sector,
          recommendations,
          totalFound: recommendations.length,
          message: recommendations.length > 0 
            ? `Found ${recommendations.length} trending projects in ${input.sector} sector`
            : `No trending projects found in ${input.sector} sector`,
        };

      } catch (error) {
        console.error('❌ Sector recommendations error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get sector recommendations',
        });
      }
    }),
});