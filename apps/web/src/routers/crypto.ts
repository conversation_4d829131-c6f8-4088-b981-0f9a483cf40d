/**
 * Crypto Router for BuddyChip tRPC API
 * 
 * Handles Cookie.fun API integration for crypto project analytics,
 * including sectors, smart followers, project search, and competitive intelligence
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { protectedProcedure, createTRPCRouter } from '../lib/trpc';
import { cookieClient } from '../lib/cookie-client';
import { checkRateLimit, recordUsage } from '../lib/db-utils';
import { FeatureType } from '../../prisma/generated/index.js';

export const cryptoRouter = createTRPCRouter({
  /**
   * Test Cookie.fun API connection
   */
  testConnection: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        console.log('🍪 CryptoRouter: Testing API connection for user:', ctx.userId);

        const result = await cookieClient.testConnection();

        return {
          success: result.success,
          message: result.message,
          availableEndpoints: result.availableEndpoints,
          timestamp: new Date().toISOString(),
        };
      } catch (error) {
        console.error('Test connection error:', error);

        return {
          success: false,
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        };
      }
    }),

  /**
   * Get all available crypto sectors
   */
  getSectors: protectedProcedure
    .query(async ({ ctx }) => {
      // Check rate limits - using AI_CALLS as these are intelligence features
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting sectors for user:', ctx.userId);

        const response = await cookieClient.getSectors();
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: response.data,
        };
      } catch (error) {
        console.error('Get sectors error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch crypto sectors',
        });
      }
    }),

  /**
   * Get smart followers for a Twitter account
   */
  getSmartFollowers: protectedProcedure
    .input(z.object({
      username: z.string().min(1, 'Username is required').optional(),
      userId: z.string().optional(),
      limit: z.number().min(1).max(50).default(20),
    }))
    .mutation(async ({ ctx, input }) => {
      if (!input.username && !input.userId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Either username or userId must be provided',
        });
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting smart followers for:', input.username || input.userId);

        const response = await cookieClient.getSmartFollowers({
          username: input.username,
          userId: input.userId,
          limit: input.limit,
        });
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
        };
      } catch (error) {
        console.error('Get smart followers error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch smart followers',
        });
      }
    }),

  /**
   * Get account feed with filtering options
   */
  getAccountFeed: protectedProcedure
    .input(z.object({
      username: z.string().min(1, 'Username is required').optional(),
      userId: z.string().optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      type: z.enum(['Original', 'Reply', 'Quote']).optional(),
      hasMedia: z.boolean().optional(),
      sortBy: z.enum(['CreatedAt', 'Impressions']).optional(),
      sortOrder: z.enum(['Ascending', 'Descending']).optional(),
      limit: z.number().min(1).max(20).default(10),
    }))
    .mutation(async ({ ctx, input }) => {
      if (!input.username && !input.userId) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Either username or userId must be provided',
        });
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting account feed for:', input.username || input.userId);

        const response = await cookieClient.getAccountFeed({
          username: input.username,
          userId: input.userId,
          startDate: input.startDate,
          endDate: input.endDate,
          type: input.type,
          hasMedia: input.hasMedia,
          sortBy: input.sortBy,
          sortOrder: input.sortOrder,
          limit: input.limit,
        });
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
        };
      } catch (error) {
        console.error('Get account feed error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch account feed',
        });
      }
    }),

  /**
   * Search for crypto projects with various filters
   */
  searchProjects: protectedProcedure
    .input(z.object({
      searchQuery: z.string().optional(),
      projectSlug: z.string().optional(),
      sectorSlug: z.string().optional(),
      type: z.enum(['Original', 'Reply', 'Quote']).optional(),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
      sortBy: z.enum(['SmartEngagementPoints', 'Impressions', 'MatchingTweetsCount', 'Mindshare']).optional(),
      sortOrder: z.enum(['Ascending', 'Descending']).optional(),
      mindshareTimeframe: z.enum(['_7Days', '_30Days']).optional(),
      granulation: z.enum(['_1Hour', '_24Hours']).optional(),
      metricType: z.enum(['Impressions', 'EngagementRate', 'Mentions']).optional(),
      limit: z.number().min(1).max(20).default(10),
    }))
    .mutation(async ({ ctx, input }) => {
      if (!input.searchQuery && !input.projectSlug) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'At least one of searchQuery or projectSlug must be provided',
        });
      }

      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Searching projects with query:', input.searchQuery || input.projectSlug);

        const response = await cookieClient.searchProjects({
          searchQuery: input.searchQuery,
          projectSlug: input.projectSlug,
          sectorSlug: input.sectorSlug,
          type: input.type,
          startDate: input.startDate,
          endDate: input.endDate,
          sortBy: input.sortBy,
          sortOrder: input.sortOrder,
          mindshareTimeframe: input.mindshareTimeframe,
          granulation: input.granulation,
          metricType: input.metricType,
          limit: input.limit,
        });
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: response.data,
          pagination: response.pagination,
          timeseries: response.timeseries,
        };
      } catch (error) {
        console.error('Search projects error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to search crypto projects',
        });
      }
    }),

  /**
   * Get trending projects in a specific sector
   */
  getTrendingProjects: protectedProcedure
    .input(z.object({
      sectorSlug: z.string().optional(),
      timeframe: z.enum(['_7Days', '_30Days']).default('_7Days'),
      limit: z.number().min(1).max(20).default(10),
    }))
    .query(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting trending projects for sector:', input.sectorSlug || 'all');

        const projects = await cookieClient.getTrendingProjects(input.sectorSlug, input.timeframe);

        console.log(`✅ Successfully fetched ${projects.length} trending projects`);

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: projects.slice(0, input.limit),
          timeframe: input.timeframe,
          sector: input.sectorSlug,
        };
      } catch (error) {
        console.error('Get trending projects error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch trending projects',
        });
      }
    }),

  /**
   * Get project metrics over time
   */
  getProjectMetrics: protectedProcedure
    .input(z.object({
      projectSlug: z.string().min(1, 'Project slug is required'),
      metricType: z.enum(['Impressions', 'EngagementRate', 'Mentions']),
      granulation: z.enum(['_1Hour', '_24Hours']),
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting metrics for project:', input.projectSlug);

        const metrics = await cookieClient.getProjectMetrics({
          projectSlug: input.projectSlug,
          metricType: input.metricType,
          granulation: input.granulation,
          startDate: input.startDate,
          endDate: input.endDate,
        });
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: metrics,
          project: input.projectSlug,
          metricType: input.metricType,
          granulation: input.granulation,
        };
      } catch (error) {
        console.error('Get project metrics error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch project metrics',
        });
      }
    }),

  /**
   * Get competitive analysis for a project
   */
  getCompetitiveAnalysis: protectedProcedure
    .input(z.object({
      projectSlug: z.string().min(1, 'Project slug is required'),
    }))
    .query(async ({ ctx, input }) => {
      // Check rate limits (this uses multiple API calls, so charge more)
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 3);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting competitive analysis for:', input.projectSlug);

        const analysis = await cookieClient.getCompetitiveAnalysis(input.projectSlug);
        
        // Record usage (3 calls since this is a complex operation)
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 3);

        return {
          success: true,
          ...analysis,
        };
      } catch (error) {
        console.error('Get competitive analysis error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch competitive analysis',
        });
      }
    }),

  /**
   * Find sector influencers using smart followers
   */
  findSectorInfluencers: protectedProcedure
    .input(z.object({
      username: z.string().min(1, 'Username is required'),
      targetSector: z.string().min(1, 'Target sector is required'),
      limit: z.number().min(1).max(50).default(10),
    }))
    .mutation(async ({ ctx, input }) => {
      // Check rate limits
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Finding sector influencers for:', input.targetSector);

        const influencers = await cookieClient.findSectorInfluencers(input.username, input.targetSector);
        
        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: influencers.slice(0, input.limit),
          sector: input.targetSector,
          sourceAccount: input.username,
        };
      } catch (error) {
        console.error('Find sector influencers error:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to find sector influencers',
        });
      }
    }),

  /**
   * Get market intelligence for AI context
   * This is used by Benji to enhance responses with market data
   */
  getMarketIntelligence: protectedProcedure
    .input(z.object({
      keywords: z.array(z.string()).optional(),
      sectorSlug: z.string().optional(),
      projectSlugs: z.array(z.string()).optional(),
      timeframe: z.enum(['_7Days', '_30Days']).default('_7Days'),
    }))
    .query(async ({ ctx, input }) => {
      // Check rate limits (lightweight call)
      const rateLimit = await checkRateLimit(ctx.userId, FeatureType.AI_CALLS, 1);
      if (!rateLimit.allowed) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: `API calls limit exceeded. ${rateLimit.remaining} remaining this month.`,
        });
      }

      try {
        console.log('🍪 CryptoRouter: Getting market intelligence for AI context');

        // Get trending projects for context
        const trendingProjects = await cookieClient.getTrendingProjects(input.sectorSlug, input.timeframe);
        
        // If specific projects are requested, get their data
        let projectData = [];
        if (input.projectSlugs && input.projectSlugs.length > 0) {
          const projectPromises = input.projectSlugs.map(slug => 
            cookieClient.searchProjects({ projectSlug: slug, limit: 1 })
          );
          const results = await Promise.all(projectPromises);
          projectData = results.map(r => r.data[0]).filter(Boolean);
        }

        // Record usage
        await recordUsage(ctx.userId, FeatureType.AI_CALLS, 1);

        return {
          success: true,
          data: {
            trendingProjects: trendingProjects.slice(0, 5), // Top 5 trending
            projectData,
            timeframe: input.timeframe,
            sector: input.sectorSlug,
            generatedAt: new Date().toISOString(),
          },
        };
      } catch (error) {
        console.error('Get market intelligence error:', error);
        
        // Don't throw error for AI context - return empty data
        return {
          success: false,
          data: {
            trendingProjects: [],
            projectData: [],
            timeframe: input.timeframe,
            sector: input.sectorSlug,
            generatedAt: new Date().toISOString(),
          },
          error: 'Failed to fetch market intelligence',
        };
      }
    }),

  /**
   * Clear Cookie.fun API cache (for testing/debugging)
   */
  clearCache: protectedProcedure
    .mutation(() => {
      cookieClient.clearCache();
      return {
        success: true,
        message: 'Cookie.fun API cache cleared',
      };
    }),

  /**
   * Get Cookie.fun API cache statistics
   */
  getCacheStats: protectedProcedure
    .query(() => {
      const stats = cookieClient.getCacheStats();
      return {
        success: true,
        cache: stats,
      };
    }),
});