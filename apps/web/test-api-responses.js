#!/usr/bin/env node

// Test actual API responses to understand the structure
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const BASE_URL = 'https://api.staging.cookie.fun';

console.log('🔍 Analyzing actual API responses...');

async function analyzeResponses() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found');
    return;
  }

  const tests = [
    {
      name: 'GET /v3/sectors',
      method: 'GET',
      endpoint: '/v3/sectors',
      body: null
    },
    {
      name: 'POST /v3/account (SmokeyTheBera)',
      method: 'POST',
      endpoint: '/v3/account',
      body: { username: 'SmokeyTheBera' }
    },
    {
      name: 'POST /v3/project (berachain)',
      method: 'POST',
      endpoint: '/v3/project',
      body: { slug: 'berachain' }
    },
    {
      name: 'POST /v3/account/smart-followers',
      method: 'POST',
      endpoint: '/v3/account/smart-followers',
      body: { username: 'SmokeyTheBera' }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`\n🔄 Testing: ${test.name}`);
      
      const requestOptions = {
        method: test.method,
        headers: {
          'X-API-Key': COOKIE_API_KEY,
          'Content-Type': 'application/json',
        },
      };

      if (test.body) {
        requestOptions.body = JSON.stringify(test.body);
      }
      
      const response = await fetch(`${BASE_URL}${test.endpoint}`, requestOptions);
      
      console.log(`📊 Status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ Success!`);
        
        // Show full response structure
        console.log('📋 Full Response:');
        console.log(JSON.stringify(data, null, 2));
        
        // Analyze structure
        console.log('\n📊 Response Analysis:');
        console.log(`- Top-level keys: ${Object.keys(data).join(', ')}`);
        
        if (data.data) {
          if (Array.isArray(data.data)) {
            console.log(`- data is array with ${data.data.length} items`);
            if (data.data[0]) {
              console.log(`- First item keys: ${Object.keys(data.data[0]).join(', ')}`);
            }
          } else {
            console.log(`- data is object with keys: ${Object.keys(data.data).join(', ')}`);
          }
        }
        
        if (data.ok !== undefined) {
          console.log(`- ok field: ${data.ok}`);
        }
        
        if (data.success !== undefined) {
          console.log(`- success field: ${data.success}`);
        }
        
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    console.log('\n' + '='.repeat(50));
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

analyzeResponses().catch(console.error);
