#!/usr/bin/env node

// Test different authentication methods for Cookie.fun staging API
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const BASE_URL = 'https://api.staging.cookie.fun';

console.log('🔑 Testing different authentication methods...');
console.log('🔗 Base URL:', BASE_URL);
console.log('🔑 API Key:', COOKIE_API_KEY?.substring(0, 10) + '...');

async function testAuthMethods() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const testEndpoint = '/v3/account';
  const testBody = { "username": "SmokeyTheBera" };

  // Different authentication methods to try
  const authMethods = [
    {
      name: 'Authorization: Bearer {key}',
      headers: {
        'Authorization': `Bearer ${COOKIE_API_KEY}`,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'Authorization: {key}',
      headers: {
        'Authorization': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'X-API-Key: {key}',
      headers: {
        'X-API-Key': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'Cookie-API-Key: {key}',
      headers: {
        'Cookie-API-Key': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'API-Key: {key}',
      headers: {
        'API-Key': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'x-api-key: {key}',
      headers: {
        'x-api-key': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'apikey: {key}',
      headers: {
        'apikey': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      }
    },
    {
      name: 'Query parameter ?apikey={key}',
      headers: {
        'Content-Type': 'application/json',
      },
      useQueryParam: true
    }
  ];

  console.log('\n🧪 Testing authentication methods...');
  
  for (const authMethod of authMethods) {
    try {
      console.log(`\n🔄 Testing: ${authMethod.name}`);
      
      let url = `${BASE_URL}${testEndpoint}`;
      if (authMethod.useQueryParam) {
        url += `?apikey=${COOKIE_API_KEY}`;
      }
      
      const response = await fetch(url, {
        method: 'POST',
        headers: authMethod.headers,
        body: JSON.stringify(testBody)
      });

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS! Found working auth method: ${authMethod.name}`);
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
        if (data.data) {
          console.log(`👤 Account: ${data.data.username || data.data.name || 'Unknown'}`);
          console.log(`👥 Followers: ${data.data.followersCount || 'N/A'}`);
        }
        
        console.log(`\n🎉 WORKING AUTH METHOD: ${authMethod.name}`);
        return authMethod;
        
      } else {
        const errorText = await response.text();
        
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.error?.errorMessage === "API key is missing") {
            console.log(`❌ Still missing API key`);
          } else if (errorJson.error?.errorMessage === "Invalid API key") {
            console.log(`🔑 API key recognized but invalid`);
          } else {
            console.log(`⚠️  Different error: ${errorJson.error?.errorMessage || errorText}`);
          }
        } catch (e) {
          console.log(`❌ Error: ${errorText}`);
        }
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n❌ No working authentication method found');
  console.log('\n💡 Suggestions:');
  console.log('1. Check Cookie.fun documentation for correct auth header');
  console.log('2. Verify the API key is valid for staging environment');
  console.log('3. Contact Cookie.fun support about authentication');
}

testAuthMethods().catch(console.error);
