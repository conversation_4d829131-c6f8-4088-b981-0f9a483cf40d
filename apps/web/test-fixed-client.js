#!/usr/bin/env node

// Test our updated Cookie client with the correct API settings
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import our updated client
const { cookieClient } = require('./src/lib/cookie-client.ts');

console.log('🍪 Testing FIXED Cookie.fun client...');

async function testFixedClient() {
  try {
    console.log('\n🔄 Testing connection...');
    const connectionTest = await cookieClient.testConnection();
    console.log('Connection result:', connectionTest);

    console.log('\n🔄 Testing getSectors...');
    const sectors = await cookieClient.getSectors();
    console.log(`✅ Sectors: ${sectors.data.length} found`);
    console.log('First sector:', sectors.data[0]);

    console.log('\n🔄 Testing getAccount...');
    const account = await cookieClient.getAccount({ username: 'SmokeyTheBera' });
    console.log(`✅ Account: ${account.data.username}`);
    console.log(`👥 Followers: ${account.data.followersCount}`);

    console.log('\n🔄 Testing getProject...');
    const project = await cookieClient.getProject({ slug: 'berachain' });
    console.log(`✅ Project: ${project.data.name}`);
    console.log(`🏷️ Symbol: ${project.data.symbol}`);
    console.log(`🏢 Sector: ${project.data.sector}`);

    console.log('\n🔄 Testing searchProjects...');
    const searchResults = await cookieClient.searchProjects({ query: 'bitcoin', page: 1, limit: 5 });
    console.log(`✅ Search results: ${searchResults.data.length} projects found`);
    if (searchResults.data[0]) {
      console.log('First result:', searchResults.data[0].name);
    }

    console.log('\n🔄 Testing getTrendingProjects...');
    const trending = await cookieClient.getTrendingProjects(undefined, '_7Days');
    console.log(`✅ Trending projects: ${trending.length} found`);
    if (trending[0]) {
      console.log('Top trending:', trending[0].name, 'Mindshare:', trending[0].mindshare);
    }

    console.log('\n🎉 ALL TESTS PASSED! Cookie.fun API is working perfectly!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testFixedClient();
