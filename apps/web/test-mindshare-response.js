#!/usr/bin/env node

// Test mindshare leaderboard response structure
const dotenv = require('dotenv');
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const BASE_URL = 'https://api.staging.cookie.fun';

console.log('🔍 Testing mindshare leaderboard response structure...');

async function testMindshareResponse() {
  try {
    const body = {
      mindshareTimeframe: '_7Days',
      sortBy: 'Mindshare',
      sortOrder: 'Descending'
    };

    const response = await fetch(`${BASE_URL}/v3/project/mindshare-leaderboard`, {
      method: 'POST',
      headers: {
        'X-API-Key': COOKIE_API_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    console.log(`📊 Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success!');
      
      // Show full response structure
      console.log('📋 Full Response Structure:');
      console.log('Top-level keys:', Object.keys(data));
      
      if (data.ok) {
        console.log('\n📊 data.ok structure:');
        console.log('data.ok keys:', Object.keys(data.ok));
        
        if (data.ok.data && Array.isArray(data.ok.data)) {
          console.log(`\n📊 data.ok.data is array with ${data.ok.data.length} items`);
          if (data.ok.data[0]) {
            console.log('First item keys:', Object.keys(data.ok.data[0]));
            console.log('First item sample:', JSON.stringify(data.ok.data[0], null, 2).substring(0, 500) + '...');
          }
        }
        
        if (data.ok.totalCount !== undefined) {
          console.log(`Total count: ${data.ok.totalCount}`);
        }
        
        if (data.ok.currentPage !== undefined) {
          console.log(`Current page: ${data.ok.currentPage}`);
        }
      }
      
      console.log('\n📋 Raw response sample:');
      console.log(JSON.stringify(data, null, 2).substring(0, 1000) + '...');
      
    } else {
      const errorText = await response.text();
      console.log(`❌ Error: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`💥 Exception: ${error.message}`);
  }
}

testMindshareResponse();
