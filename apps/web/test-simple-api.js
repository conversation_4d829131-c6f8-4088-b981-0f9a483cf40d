#!/usr/bin/env node

// Simple test to verify our Cookie client is working
const dotenv = require('dotenv');
dotenv.config();

const { cookieClient } = require('./src/lib/cookie-client.ts');

console.log('🍪 Simple Cookie.fun API test...');

async function simpleTest() {
  try {
    console.log('\n🔄 Testing getAccount...');
    const account = await cookieClient.getAccount({ username: 'SmokeyTheBera' });
    console.log('✅ Account response received');
    console.log('Response keys:', Object.keys(account));
    
    // Show the actual structure
    if (account.twitterUsername) {
      console.log(`👤 Username: ${account.twitterUsername}`);
      console.log(`👥 Followers: ${account.followersCount}`);
      console.log(`✅ Account data structure is correct!`);
    } else {
      console.log('Account structure:', JSON.stringify(account, null, 2).substring(0, 500) + '...');
    }

    console.log('\n🔄 Testing getProject...');
    const project = await cookieClient.getProject({ slug: 'be<PERSON>hain' });
    console.log('✅ Project response received');
    console.log('Response keys:', Object.keys(project));
    
    if (project.name) {
      console.log(`🚀 Project: ${project.name}`);
      console.log(`🏷️ Symbol: ${project.symbol || 'N/A'}`);
      console.log(`✅ Project data structure is correct!`);
    } else {
      console.log('Project structure:', JSON.stringify(project, null, 2).substring(0, 500) + '...');
    }

    console.log('\n🎉 Cookie.fun API is working correctly!');
    console.log('✅ Base URL: https://api.staging.cookie.fun');
    console.log('✅ Authentication: X-API-Key header');
    console.log('✅ Response format: { ok: data, success: true, error: null }');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

simpleTest();
