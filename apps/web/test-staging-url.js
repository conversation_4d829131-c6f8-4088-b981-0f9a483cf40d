#!/usr/bin/env node

// Test the staging Cookie.fun API URL
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const COOKIE_API_KEY = process.env.COOKIE_API_KEY;
const STAGING_BASE_URL = 'https://api.staging.cookie.fun';

console.log('🍪 Testing Cookie.fun STAGING API...');
console.log('🔗 Base URL:', STAGING_BASE_URL);
console.log('🔑 API Key:', COOKIE_API_KEY?.substring(0, 10) + '...');

async function testStagingAPI() {
  if (!COOKIE_API_KEY) {
    console.log('❌ COOKIE_API_KEY not found in environment variables');
    return;
  }

  const testCases = [
    {
      name: 'POST /v3/account (SmokeyTheBera)',
      endpoint: '/v3/account',
      body: { "username": "SmokeyTheBera" }
    },
    {
      name: 'POST /v3/project (be<PERSON>hain)',
      endpoint: '/v3/project',
      body: { "slug": "berachain" }
    },
    {
      name: 'POST /v3/account/smart-followers (SmokeyTheBera)',
      endpoint: '/v3/account/smart-followers',
      body: { "username": "SmokeyTheBera" }
    },
    {
      name: 'GET /v3/sectors',
      endpoint: '/v3/sectors',
      body: null,
      method: 'GET'
    },
    {
      name: 'POST /v3/project/search',
      endpoint: '/v3/project/search',
      body: { "query": "bitcoin", "page": 1, "limit": 20 }
    },
    {
      name: 'POST /v3/project/mindshare-leaderboard',
      endpoint: '/v3/project/mindshare-leaderboard',
      body: {
        "mindshareTimeframe": "_30Days",
        "sortBy": "Mindshare",
        "sortOrder": "Descending"
      }
    }
  ];

  console.log('\n🧪 Testing staging endpoints...');
  
  for (const testCase of testCases) {
    try {
      console.log(`\n🔄 Testing: ${testCase.name}`);
      
      const requestOptions = {
        method: testCase.method || 'POST',
        headers: {
          'Authorization': `Bearer ${COOKIE_API_KEY}`,
          'Content-Type': 'application/json',
        },
      };

      if (testCase.body) {
        requestOptions.body = JSON.stringify(testCase.body);
        console.log(`📋 Body: ${JSON.stringify(testCase.body, null, 2)}`);
      }
      
      const response = await fetch(`${STAGING_BASE_URL}${testCase.endpoint}`, requestOptions);

      console.log(`📊 Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ SUCCESS!`);
        
        // Show response structure
        console.log(`📋 Response keys: ${Object.keys(data).join(', ')}`);
        
        // Show specific data based on endpoint
        if (testCase.endpoint.includes('/account/smart-followers')) {
          if (data.data && Array.isArray(data.data)) {
            console.log(`👥 Smart followers count: ${data.data.length}`);
            if (data.data[0]) {
              console.log(`📈 Top follower: ${data.data[0].username || data.data[0].name || 'Unknown'}`);
            }
          }
        } else if (testCase.endpoint.includes('/account')) {
          if (data.data) {
            console.log(`👤 Account: ${data.data.username || data.data.name || 'Unknown'}`);
            console.log(`👥 Followers: ${data.data.followersCount || 'N/A'}`);
          }
        } else if (testCase.endpoint.includes('/project') && !testCase.endpoint.includes('/search')) {
          if (data.data) {
            console.log(`🚀 Project: ${data.data.name || data.data.slug || 'Unknown'}`);
            console.log(`🏷️ Symbol: ${data.data.symbol || 'N/A'}`);
            console.log(`🏢 Sector: ${data.data.sector || 'N/A'}`);
          }
        } else if (testCase.endpoint.includes('/sectors')) {
          if (data.data && Array.isArray(data.data)) {
            console.log(`🏢 Sectors count: ${data.data.length}`);
            if (data.data[0]) {
              console.log(`📈 First sector: ${data.data[0].name || data.data[0].slug || 'Unknown'}`);
            }
          }
        } else if (data.data && Array.isArray(data.data)) {
          console.log(`📊 Data count: ${data.data.length}`);
          if (data.data[0]) {
            console.log(`📈 First item: ${data.data[0].name || data.data[0].slug || 'Unknown'}`);
          }
        }
        
      } else {
        const errorText = await response.text();
        console.log(`❌ Error: ${errorText}`);
        
        // Try to parse error as JSON
        try {
          const errorJson = JSON.parse(errorText);
          console.log(`📋 Error details: ${JSON.stringify(errorJson, null, 2)}`);
        } catch (e) {
          // Error text is not JSON
        }
      }
      
    } catch (error) {
      console.log(`💥 Exception: ${error.message}`);
    }
    
    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1500));
  }
  
  console.log('\n📋 Test Summary:');
  console.log('If endpoints work, we found the correct base URL!');
}

testStagingAPI().catch(console.error);
