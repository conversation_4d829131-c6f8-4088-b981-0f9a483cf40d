#!/usr/bin/env node

// Test trending projects with real data
const dotenv = require('dotenv');
dotenv.config();

const { cookieClient } = require('./src/lib/cookie-client.ts');

console.log('🔥 Testing REAL trending projects data...');

async function testTrendingReal() {
  try {
    console.log('\n🔄 Testing getTrendingProjects...');
    const trending = await cookieClient.getTrendingProjects(undefined, '_7Days');
    
    if (trending && trending.length > 0) {
      console.log(`✅ Got ${trending.length} real trending projects!`);
      console.log('\n📊 Top 3 trending projects:');
      
      trending.slice(0, 3).forEach((project, index) => {
        console.log(`${index + 1}. ${project.name} (${project.symbol})`);
        console.log(`   Mindshare: ${project.mindshare}%`);
        console.log(`   Sector: ${project.sector}`);
        console.log(`   Description: ${project.description}`);
        console.log('');
      });
      
      console.log('🎉 SUCCESS: Real trending data is working!');
    } else {
      console.log('❌ No trending projects returned');
    }

    console.log('\n🔄 Testing getSectors...');
    const sectors = await cookieClient.getSectors();
    
    if (sectors && sectors.data && sectors.data.length > 0) {
      console.log(`✅ Got ${sectors.data.length} real sectors!`);
      console.log('\n📊 Available sectors:');
      
      sectors.data.forEach((sector, index) => {
        console.log(`${index + 1}. ${sector.name} (${sector.slug})`);
      });
      
      console.log('🎉 SUCCESS: Real sectors data is working!');
    } else {
      console.log('❌ No sectors returned');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('This means the API is not returning real data properly');
  }
}

testTrendingReal();
